#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
from docx import Document
from openpyxl import load_workbook
from PyPDF2 import PdfReader, PdfWriter
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import io

class WatermarkProcessor:
    def __init__(self, watermark_text="机密文档", font_path="/usr/share/fonts/noto-cjk/NotoSansCJK-Regular.ttc"):
        self.watermark_text = watermark_text
        self.font_path = font_path
        
        # 注册PDF中文字体
        try:
            pdfmetrics.registerFont(TTFont('NotoSansCJK', font_path))
        except:
            print("警告: 无法加载中文字体，PDF水印可能显示异常")
    
    def add_watermark_to_png(self, input_path, output_path):
        """为PNG图片添加水印"""
        try:
            image = Image.open(input_path).convert("RGBA")
            watermark = Image.new("RGBA", image.size, (0,0,0,0))
            draw = ImageDraw.Draw(watermark)
            
            try:
                font = ImageFont.truetype(self.font_path, min(image.width, image.height) // 15)
            except:
                font = ImageFont.load_default()
            
            bbox = draw.textbbox((0, 0), self.watermark_text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (image.width - text_width) // 2
            y = (image.height - text_height) // 2
            
            draw.text((x, y), self.watermark_text, font=font, fill=(255, 255, 255, 128))
            
            watermarked = Image.alpha_composite(image, watermark)
            watermarked.convert("RGB").save(output_path, "PNG")
            print(f"✓ PNG水印添加完成: {output_path}")
            
        except Exception as e:
            print(f"✗ PNG水印添加失败 {input_path}: {e}")
    
    def add_watermark_to_word(self, input_path, output_path):
        """为Word文档添加水印"""
        try:
            doc = Document(input_path)
            section = doc.sections[0]
            header = section.header
            
            if not header.paragraphs:
                paragraph = header.add_paragraph()
            else:
                paragraph = header.paragraphs[0]
                
            paragraph.text = self.watermark_text
            paragraph.alignment = 1  # 居中对齐
            
            doc.save(output_path)
            print(f"✓ Word水印添加完成: {output_path}")
            
        except Exception as e:
            print(f"✗ Word水印添加失败 {input_path}: {e}")
    
    def add_watermark_to_excel(self, input_path, output_path):
        """为Excel文件添加水印"""
        try:
            wb = load_workbook(input_path)
            
            # 创建水印图片
            img = Image.new('RGBA', (400, 100), (255, 255, 255, 0))
            draw = ImageDraw.Draw(img)
            
            try:
                font = ImageFont.truetype(self.font_path, 24)
            except:
                font = ImageFont.load_default()
                
            draw.text((50, 30), self.watermark_text, font=font, fill=(128, 128, 128, 128))
            
            # 为每个工作表添加水印
            for sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
                
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='PNG')
                img_buffer.seek(0)
                
                from openpyxl.drawing.image import Image as OpenpyxlImage
                openpyxl_img = OpenpyxlImage(img_buffer)
                ws.add_image(openpyxl_img, 'A1')
            
            wb.save(output_path)
            print(f"✓ Excel水印添加完成: {output_path}")
            
        except Exception as e:
            print(f"✗ Excel水印添加失败 {input_path}: {e}")
    
    def add_watermark_to_pdf(self, input_path, output_path):
        """为PDF文件添加水印"""
        try:
            # 创建水印PDF
            watermark_buffer = io.BytesIO()
            c = canvas.Canvas(watermark_buffer, pagesize=letter)
            
            try:
                c.setFont('NotoSansCJK', 50)
            except:
                c.setFont('Helvetica', 50)
                
            c.setFillColorRGB(0.5, 0.5, 0.5, alpha=0.3)
            c.rotate(45)
            c.drawString(200, 100, self.watermark_text)
            c.save()
            
            # 合并水印
            watermark_buffer.seek(0)
            watermark_pdf = PdfReader(watermark_buffer)
            input_pdf = PdfReader(input_path)
            
            output_pdf = PdfWriter()
            
            for page_num in range(len(input_pdf.pages)):
                page = input_pdf.pages[page_num]
                page.merge_page(watermark_pdf.pages[0])
                output_pdf.add_page(page)
            
            with open(output_path, 'wb') as output_file:
                output_pdf.write(output_file)
                
            print(f"✓ PDF水印添加完成: {output_path}")
            
        except Exception as e:
            print(f"✗ PDF水印添加失败 {input_path}: {e}")
    
    def process_file(self, input_path, output_dir=None):
        """处理单个文件"""
        input_path = Path(input_path)
        
        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            output_path = output_dir / f"watermarked_{input_path.name}"
        else:
            output_path = input_path.parent / f"watermarked_{input_path.name}"
        
        ext = input_path.suffix.lower()
        
        if ext == '.png':
            self.add_watermark_to_png(str(input_path), str(output_path))
        elif ext in ['.docx', '.doc']:
            if ext == '.doc':
                print(f"警告: .doc格式支持有限，建议转换为.docx格式")
            self.add_watermark_to_word(str(input_path), str(output_path))
        elif ext in ['.xlsx', '.xls']:
            self.add_watermark_to_excel(str(input_path), str(output_path))
        elif ext == '.pdf':
            self.add_watermark_to_pdf(str(input_path), str(output_path))
        else:
            print(f"✗ 不支持的文件格式: {ext}")
    
    def process_directory(self, input_dir, output_dir=None):
        """批量处理目录中的文件"""
        input_dir = Path(input_dir)
        supported_extensions = {'.png', '.docx', '.doc', '.xlsx', '.xls', '.pdf'}
        
        files_found = []
        for ext in supported_extensions:
            files_found.extend(input_dir.glob(f"*{ext}"))
            files_found.extend(input_dir.glob(f"*{ext.upper()}"))
        
        if not files_found:
            print("未找到支持的文件格式")
            return
        
        print(f"找到 {len(files_found)} 个文件需要处理")
        
        for file_path in files_found:
            self.process_file(file_path, output_dir)

def main():
    if len(sys.argv) < 2:
        print("用法:")
        print(f"  {sys.argv[0]} <输入文件/目录> [输出目录] [水印文字]")
        print("\n示例:")
        print(f"  {sys.argv[0]} document.pdf")
        print(f"  {sys.argv[0]} /path/to/files/ /path/to/output/ '我的水印'")
        sys.exit(1)
    
    input_path = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else None
    watermark_text = sys.argv[3] if len(sys.argv) > 3 else "机密文档"
    
    processor = WatermarkProcessor(watermark_text)
    
    if os.path.isfile(input_path):
        processor.process_file(input_path, output_dir)
    elif os.path.isdir(input_path):
        processor.process_directory(input_path, output_dir)
    else:
        print(f"错误: 路径不存在 {input_path}")
        sys.exit(1)

if __name__ == "__main__":
    main()
